# -*- coding: utf-8 -*-
"""
南京欧美同学会爬虫配置文件
"""

# 目标网站配置
TARGET_URL = "https://www.njwrsa.org.cn/about/10.html"

# XPath配置 - 用户提供的xpath路径
CONTENT_XPATH = "/html/body/div[3]/div/div[2]/div/div[2]"

# 输出文件配置
OUTPUT_FILENAME = "南京欧美同学会_本会简介.xlsx"

# 请求头配置
HEADERS = {
    'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/91.0.4472.124 Safari/537.36',
    'Accept': 'text/html,application/xhtml+xml,application/xml;q=0.9,image/webp,*/*;q=0.8',
    'Accept-Language': 'zh-CN,zh;q=0.9,en;q=0.8',
    'Accept-Encoding': 'gzip, deflate, br',
    'Connection': 'keep-alive',
    'Upgrade-Insecure-Requests': '1',
}

# 请求超时设置（秒）
REQUEST_TIMEOUT = 30
