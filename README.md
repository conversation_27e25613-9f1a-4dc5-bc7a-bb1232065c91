# 南京欧美同学会网站爬虫

## 项目简介

本项目是一个专门用于爬取南京欧美同学会官网"本会简介"页面内容的Python爬虫工具。爬取的内容将自动保存为Excel格式，便于后续处理和分析。

## 功能特点

- 🎯 **精准提取**: 使用用户指定的XPath路径精确提取目标内容
- 📊 **Excel输出**: 自动将爬取内容格式化并保存为Excel文件
- 🔄 **备用方案**: 当XPath提取失败时，自动使用CSS选择器作为备用方案
- 🧹 **文本清理**: 自动清理多余的空白字符和特殊字符
- ⏰ **时间记录**: 记录爬取时间和来源网址
- 🛡️ **错误处理**: 完善的异常处理机制

## 目标网站

- **网站**: 南京欧美同学会官网
- **目标页面**: https://www.njwrsa.org.cn/about/10.html
- **提取路径**: /html/body/div[3]/div/div[2]/div/div[2]

## 安装依赖

```bash
pip install -r requirements.txt
```

## 使用方法

### 1. 直接运行

```bash
python spider_njwrsa.py
```

### 2. 配置修改

如需修改目标URL或XPath路径，请编辑 `config.py` 文件：

```python
# 修改目标URL
TARGET_URL = "你的目标网址"

# 修改XPath路径
CONTENT_XPATH = "你的xpath路径"

# 修改输出文件名
OUTPUT_FILENAME = "你的文件名.xlsx"
```

## 输出文件

爬虫运行成功后，会在当前目录生成 `南京欧美同学会_本会简介.xlsx` 文件，包含以下信息：

| 列名 | 说明 |
|------|------|
| 标题 | 页面标题 |
| 内容 | 提取的正文内容 |
| 爬取时间 | 数据获取时间 |
| 来源网址 | 原始页面URL |

## 项目结构

```
Spider_tzb/
├── spider_njwrsa.py      # 主爬虫脚本
├── config.py             # 配置文件
├── requirements.txt      # 依赖包列表
├── README.md            # 项目说明文档
└── 南京欧美同学会_本会简介.xlsx  # 输出文件（运行后生成）
```

## 技术栈

- **Python 3.7+**
- **requests**: HTTP请求库
- **BeautifulSoup4**: HTML解析库
- **pandas**: 数据处理库
- **openpyxl**: Excel文件操作库
- **lxml**: XML/HTML解析库

## 注意事项

1. 请确保网络连接正常
2. 目标网站可能有反爬虫机制，建议适当增加请求间隔
3. 如果XPath路径发生变化，请及时更新配置文件
4. 建议在使用前先测试网站的可访问性

## 许可证

本项目仅供学习和研究使用，请遵守相关网站的robots.txt协议和使用条款。
