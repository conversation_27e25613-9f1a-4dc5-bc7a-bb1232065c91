# -*- coding: utf-8 -*-
"""
南京欧美同学会网站爬虫 - 简化版本
功能：爬取本会简介页面内容并输出为Excel格式
"""

import requests
from bs4 import BeautifulSoup
import csv
import re
import time
from datetime import datetime
import config

class NJWRSASpider:
    def __init__(self):
        self.session = requests.Session()
        self.session.headers.update(config.HEADERS)
    
    def fetch_page(self, url):
        """
        获取网页内容
        """
        try:
            print(f"正在访问: {url}")
            response = self.session.get(url, timeout=config.REQUEST_TIMEOUT)
            response.raise_for_status()
            response.encoding = 'utf-8'
            print("页面获取成功")
            return response.text
        except requests.RequestException as e:
            print(f"页面获取失败: {e}")
            return None
    
    def extract_content_by_css(self, html_content):
        """
        使用CSS选择器提取内容
        """
        try:
            soup = BeautifulSoup(html_content, 'html.parser')
            
            # 查找包含关键文字的内容区域
            all_text = soup.get_text()
            
            # 查找包含"南京欧美同学会"的段落
            paragraphs = soup.find_all(['p', 'div'])
            content_parts = []
            
            for para in paragraphs:
                text = para.get_text(strip=True)
                if '南京欧美同学会' in text and len(text) > 50:
                    content_parts.append(text)
            
            if content_parts:
                return '\n\n'.join(content_parts)
            
            # 如果没找到，尝试查找主要内容区域
            main_content = soup.find('div', class_=lambda x: x and ('content' in x.lower() or 'main' in x.lower()))
            if main_content:
                return main_content.get_text(strip=True)
            
            # 最后尝试从整个页面中提取相关内容
            if '南京欧美同学会' in all_text:
                # 简单的文本提取
                lines = all_text.split('\n')
                relevant_lines = []
                for line in lines:
                    line = line.strip()
                    if line and ('南京欧美同学会' in line or '留学人员' in line or '欧美同学会' in line):
                        if len(line) > 20:  # 过滤掉太短的行
                            relevant_lines.append(line)
                
                if relevant_lines:
                    return '\n'.join(relevant_lines[:10])  # 取前10行相关内容
            
            return None
        except Exception as e:
            print(f"内容解析失败: {e}")
            return None
    
    def clean_text(self, text):
        """
        清理文本内容
        """
        if not text:
            return ""
        
        # 移除多余的空白字符
        text = re.sub(r'\s+', ' ', text)
        # 移除特殊字符
        text = re.sub(r'[\r\n\t]', ' ', text)
        # 移除多余的空格
        text = ' '.join(text.split())
        
        return text.strip()
    
    def save_to_csv(self, content, filename):
        """
        将内容保存为CSV文件
        """
        try:
            csv_filename = filename.replace('.xlsx', '.csv')
            
            with open(csv_filename, 'w', newline='', encoding='utf-8-sig') as csvfile:
                writer = csv.writer(csvfile)
                
                # 写入表头
                writer.writerow(['标题', '内容', '爬取时间', '来源网址'])
                
                # 写入数据
                writer.writerow([
                    '南京欧美同学会本会简介',
                    content,
                    datetime.now().strftime('%Y-%m-%d %H:%M:%S'),
                    config.TARGET_URL
                ])
            
            print(f"内容已成功保存到: {csv_filename}")
            return True
        except Exception as e:
            print(f"保存CSV文件失败: {e}")
            return False
    
    def run(self):
        """
        运行爬虫主程序
        """
        print("=" * 50)
        print("南京欧美同学会网站爬虫启动")
        print("=" * 50)
        
        # 获取页面内容
        html_content = self.fetch_page(config.TARGET_URL)
        if not html_content:
            print("无法获取页面内容，程序退出")
            return False
        
        # 提取内容
        print("正在提取页面内容...")
        content = self.extract_content_by_css(html_content)
        
        if not content:
            print("内容提取失败，程序退出")
            return False
        
        # 清理文本
        content = self.clean_text(content)
        print(f"成功提取内容，长度: {len(content)} 字符")
        print("内容预览:")
        print("-" * 30)
        print(content[:300] + "..." if len(content) > 300 else content)
        print("-" * 30)
        
        # 保存为CSV文件
        success = self.save_to_csv(content, config.OUTPUT_FILENAME)
        
        if success:
            print("=" * 50)
            print("爬虫任务完成！")
            print(f"输出文件: {config.OUTPUT_FILENAME.replace('.xlsx', '.csv')}")
            print("=" * 50)
            return True
        else:
            print("保存文件失败")
            return False

def main():
    """
    主函数
    """
    spider = NJWRSASpider()
    spider.run()

if __name__ == "__main__":
    main()
