# -*- coding: utf-8 -*-
"""
南京欧美同学会网站爬虫
功能：爬取本会简介页面内容并输出为Excel格式
"""

import requests
from bs4 import BeautifulSoup
import pandas as pd
from lxml import html
import re
import time
from datetime import datetime
import config

class NJWRSASpider:
    def __init__(self):
        self.session = requests.Session()
        self.session.headers.update(config.HEADERS)
    
    def fetch_page(self, url):
        """
        获取网页内容
        """
        try:
            print(f"正在访问: {url}")
            response = self.session.get(url, timeout=config.REQUEST_TIMEOUT)
            response.raise_for_status()
            response.encoding = 'utf-8'
            print("页面获取成功")
            return response.text
        except requests.RequestException as e:
            print(f"页面获取失败: {e}")
            return None
    
    def extract_content_by_xpath(self, html_content, xpath):
        """
        使用xpath提取内容
        """
        try:
            tree = html.fromstring(html_content)
            elements = tree.xpath(xpath)
            
            if elements:
                # 获取元素的文本内容
                content = elements[0].text_content()
                return content.strip()
            else:
                print(f"未找到xpath路径对应的内容: {xpath}")
                return None
        except Exception as e:
            print(f"xpath解析失败: {e}")
            return None
    
    def extract_content_by_css(self, html_content):
        """
        使用CSS选择器作为备用方案提取内容
        """
        try:
            soup = BeautifulSoup(html_content, 'html.parser')
            
            # 尝试多种可能的选择器
            selectors = [
                'div.content',
                'div.main-content', 
                'div[class*="content"]',
                'div[class*="main"]',
                '.article-content',
                '.text-content'
            ]
            
            for selector in selectors:
                content_div = soup.select_one(selector)
                if content_div:
                    return content_div.get_text(strip=True)
            
            # 如果以上都没找到，尝试查找包含关键文字的div
            all_divs = soup.find_all('div')
            for div in all_divs:
                text = div.get_text(strip=True)
                if '南京欧美同学会' in text and len(text) > 100:
                    return text
            
            return None
        except Exception as e:
            print(f"CSS选择器解析失败: {e}")
            return None
    
    def clean_text(self, text):
        """
        清理文本内容
        """
        if not text:
            return ""
        
        # 移除多余的空白字符
        text = re.sub(r'\s+', ' ', text)
        # 移除特殊字符
        text = re.sub(r'[\r\n\t]', ' ', text)
        # 移除多余的空格
        text = ' '.join(text.split())
        
        return text.strip()
    
    def save_to_excel(self, content, filename):
        """
        将内容保存为Excel文件
        """
        try:
            # 创建数据框
            data = {
                '标题': ['南京欧美同学会本会简介'],
                '内容': [content],
                '爬取时间': [datetime.now().strftime('%Y-%m-%d %H:%M:%S')],
                '来源网址': [config.TARGET_URL]
            }
            
            df = pd.DataFrame(data)
            
            # 保存为Excel文件
            with pd.ExcelWriter(filename, engine='openpyxl') as writer:
                df.to_excel(writer, sheet_name='本会简介', index=False)
                
                # 调整列宽
                worksheet = writer.sheets['本会简介']
                worksheet.column_dimensions['A'].width = 20
                worksheet.column_dimensions['B'].width = 80
                worksheet.column_dimensions['C'].width = 20
                worksheet.column_dimensions['D'].width = 40
            
            print(f"内容已成功保存到: {filename}")
            return True
        except Exception as e:
            print(f"保存Excel文件失败: {e}")
            return False
    
    def run(self):
        """
        运行爬虫主程序
        """
        print("=" * 50)
        print("南京欧美同学会网站爬虫启动")
        print("=" * 50)
        
        # 获取页面内容
        html_content = self.fetch_page(config.TARGET_URL)
        if not html_content:
            print("无法获取页面内容，程序退出")
            return False
        
        # 使用xpath提取内容
        print("正在使用xpath提取内容...")
        content = self.extract_content_by_xpath(html_content, config.CONTENT_XPATH)
        
        # 如果xpath提取失败，使用CSS选择器作为备用方案
        if not content:
            print("xpath提取失败，尝试使用CSS选择器...")
            content = self.extract_content_by_css(html_content)
        
        if not content:
            print("内容提取失败，程序退出")
            return False
        
        # 清理文本
        content = self.clean_text(content)
        print(f"成功提取内容，长度: {len(content)} 字符")
        print("内容预览:")
        print("-" * 30)
        print(content[:200] + "..." if len(content) > 200 else content)
        print("-" * 30)
        
        # 保存为Excel文件
        success = self.save_to_excel(content, config.OUTPUT_FILENAME)
        
        if success:
            print("=" * 50)
            print("爬虫任务完成！")
            print(f"输出文件: {config.OUTPUT_FILENAME}")
            print("=" * 50)
            return True
        else:
            print("保存文件失败")
            return False

def main():
    """
    主函数
    """
    spider = NJWRSASpider()
    spider.run()

if __name__ == "__main__":
    main()
